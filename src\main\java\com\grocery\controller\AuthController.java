package com.grocery.controller;

import com.grocery.dto.CustomerDTO;
import com.grocery.security.SqlInjectionPreventionUtil;
import com.grocery.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;

@Controller
public class AuthController {
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private SqlInjectionPreventionUtil sqlInjectionPreventionUtil;
    
    /**
     * Home page
     */
    @GetMapping("/")
    public String home() {
        return "redirect:/login";
    }
    
    /**
     * Login page
     */
    @GetMapping("/login")
    public String login(@RequestParam(value = "error", required = false) String error,
                       @RequestParam(value = "message", required = false) String message,
                       @RequestParam(value = "logout", required = false) String logout,
                       Model model) {
        
        if (error != null) {
            model.addAttribute("error", true);
            model.addAttribute("message", message != null ? message : "Please Enter Correct UserName and Password");
        }
        
        if (logout != null) {
            model.addAttribute("logout", true);
            model.addAttribute("message", "You have been logged out successfully.");
        }
        
        return "login";
    }
    
    /**
     * Registration page
     */
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("customerDTO", new CustomerDTO());
        return "register";
    }
    
    /**
     * Customer registration
     */
    @PostMapping("/customer/register")
    public String registerCustomer(@Valid @ModelAttribute("customerDTO") CustomerDTO customerDTO,
                                  BindingResult bindingResult,
                                  Model model,
                                  RedirectAttributes redirectAttributes) {
        
        // Validate input for SQL injection
        if (!sqlInjectionPreventionUtil.validateInput(customerDTO.getFullName(), 100) ||
            !sqlInjectionPreventionUtil.isEmailSafe(customerDTO.getEmail()) ||
            !sqlInjectionPreventionUtil.validateInput(customerDTO.getAddress(), 255) ||
            !sqlInjectionPreventionUtil.isPhoneNumberSafe(customerDTO.getContactNumber())) {
            
            model.addAttribute("error", "Invalid input detected. Please check your data.");
            return "register";
        }
        
        if (bindingResult.hasErrors()) {
            return "register";
        }
        
        try {
            CustomerDTO savedCustomer = customerService.customerRegistration(customerDTO);
            redirectAttributes.addFlashAttribute("success", 
                "Registration successful! Customer ID: " + savedCustomer.getCustomerId() + 
                ". Please login with your email and password.");
            return "redirect:/login";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "register";
        }
    }
    
    /**
     * Dashboard redirect based on user role
     */
    @GetMapping("/dashboard")
    public String dashboard() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        if (auth != null && auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"))) {
            return "redirect:/admin/dashboard";
        } else if (auth != null && auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_CUSTOMER"))) {
            return "redirect:/customer/dashboard";
        }
        
        return "redirect:/login";
    }
    
    /**
     * Admin dashboard
     */
    @GetMapping("/admin/dashboard")
    public String adminDashboard(Model model) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        model.addAttribute("username", auth.getName());
        return "admin/dashboard";
    }
    
    /**
     * Customer dashboard
     */
    @GetMapping("/customer/dashboard")
    public String customerDashboard(Model model) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        model.addAttribute("username", auth.getName());
        return "customer/dashboard";
    }
    
    /**
     * Access denied page
     */
    @GetMapping("/access-denied")
    public String accessDenied() {
        return "access-denied";
    }
}
