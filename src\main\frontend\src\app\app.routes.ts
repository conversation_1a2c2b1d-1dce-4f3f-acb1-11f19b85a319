import { Routes } from '@angular/router';

export const routes: Routes = [
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { 
    path: 'login', 
    loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)
  },
  { 
    path: 'register', 
    loadComponent: () => import('./components/register/register.component').then(m => m.RegisterComponent)
  },
  { 
    path: 'customer-search', 
    loadComponent: () => import('./components/customer-search/customer-search.component').then(m => m.CustomerSearchComponent)
  },
  { 
    path: 'product-search', 
    loadComponent: () => import('./components/product-search/product-search.component').then(m => m.ProductSearchComponent)
  },
  { 
    path: 'admin-dashboard', 
    loadComponent: () => import('./components/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent)
  },
  { 
    path: 'customer-dashboard', 
    loadComponent: () => import('./components/customer-dashboard/customer-dashboard.component').then(m => m.CustomerDashboardComponent)
  },
  { path: '**', redirectTo: '/login' }
];
