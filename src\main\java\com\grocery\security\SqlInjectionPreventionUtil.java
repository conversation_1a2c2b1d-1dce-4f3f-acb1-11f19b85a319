package com.grocery.security;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * SQL Injection Prevention Utility - US0010
 * This utility class provides methods to prevent SQL injection attacks
 * by validating and sanitizing user input.
 */
@Component
public class SqlInjectionPreventionUtil {
    
    // Common SQL injection patterns
    private static final Pattern[] SQL_INJECTION_PATTERNS = {
        Pattern.compile("('|(\\-\\-)|(;)|(\\|)|(\\*)|(%))", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(union|select|insert|update|delete|drop|create|alter|exec|execute)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(script|javascript|vbscript|onload|onerror|onclick)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(<|>|&lt;|&gt;)", Pattern.CASE_INSENSITIVE)
    };
    
    // XSS patterns
    private static final Pattern[] XSS_PATTERNS = {
        Pattern.compile("<script", Pattern.CASE_INSENSITIVE),
        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onload=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onerror=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onclick=", Pattern.CASE_INSENSITIVE)
    };
    
    /**
     * Validates input for SQL injection attempts
     * @param input The input string to validate
     * @return true if input is safe, false if potentially malicious
     */
    public boolean isInputSafe(String input) {
        if (input == null || input.trim().isEmpty()) {
            return true;
        }
        
        String cleanInput = input.trim();
        
        // Check for SQL injection patterns
        for (Pattern pattern : SQL_INJECTION_PATTERNS) {
            if (pattern.matcher(cleanInput).find()) {
                return false;
            }
        }
        
        // Check for XSS patterns
        for (Pattern pattern : XSS_PATTERNS) {
            if (pattern.matcher(cleanInput).find()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Sanitizes input by removing potentially dangerous characters
     * @param input The input string to sanitize
     * @return Sanitized string
     */
    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        String sanitized = input.trim();
        
        // Remove SQL injection characters
        sanitized = sanitized.replaceAll("['\"\\-;|*%]", "");
        
        // Remove script tags and javascript
        sanitized = sanitized.replaceAll("(?i)<script.*?>.*?</script>", "");
        sanitized = sanitized.replaceAll("(?i)javascript:", "");
        sanitized = sanitized.replaceAll("(?i)vbscript:", "");
        
        // Remove event handlers
        sanitized = sanitized.replaceAll("(?i)on\\w+\\s*=", "");
        
        return sanitized;
    }
    
    /**
     * Validates email format and checks for injection attempts
     * @param email Email to validate
     * @return true if email is valid and safe
     */
    public boolean isEmailSafe(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        // Basic email format validation
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        if (!email.matches(emailRegex)) {
            return false;
        }
        
        return isInputSafe(email);
    }
    
    /**
     * Validates phone number format and checks for injection attempts
     * @param phoneNumber Phone number to validate
     * @return true if phone number is valid and safe
     */
    public boolean isPhoneNumberSafe(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }
        
        // Phone number should be exactly 10 digits
        String phoneRegex = "^\\d{10}$";
        if (!phoneNumber.matches(phoneRegex)) {
            return false;
        }
        
        return isInputSafe(phoneNumber);
    }
    
    /**
     * Validates numeric input and checks for injection attempts
     * @param numericInput Numeric input to validate
     * @return true if input is valid numeric and safe
     */
    public boolean isNumericInputSafe(String numericInput) {
        if (numericInput == null || numericInput.trim().isEmpty()) {
            return false;
        }
        
        // Check if it's a valid number
        try {
            Double.parseDouble(numericInput);
        } catch (NumberFormatException e) {
            return false;
        }
        
        return isInputSafe(numericInput);
    }
    
    /**
     * Validates and sanitizes search input
     * @param searchInput Search input to validate
     * @return Sanitized search input or null if unsafe
     */
    public String validateAndSanitizeSearchInput(String searchInput) {
        if (searchInput == null || searchInput.trim().isEmpty()) {
            return null;
        }
        
        if (!isInputSafe(searchInput)) {
            throw new SecurityException("Invalid input detected. Potential security threat.");
        }
        
        return sanitizeInput(searchInput);
    }
    
    /**
     * Validates input length to prevent buffer overflow attacks
     * @param input Input to validate
     * @param maxLength Maximum allowed length
     * @return true if input length is within limits
     */
    public boolean isInputLengthValid(String input, int maxLength) {
        if (input == null) {
            return true;
        }
        
        return input.length() <= maxLength;
    }
    
    /**
     * Comprehensive input validation
     * @param input Input to validate
     * @param maxLength Maximum allowed length
     * @return true if input passes all validation checks
     */
    public boolean validateInput(String input, int maxLength) {
        return isInputSafe(input) && isInputLengthValid(input, maxLength);
    }
}
