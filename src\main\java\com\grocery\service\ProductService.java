package com.grocery.service;

import com.grocery.dto.ProductDTO;
import com.grocery.model.Product;
import com.grocery.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    /**
     * Register Product - US007
     */
    public ProductDTO productRegistration(ProductDTO productDTO) {
        // Validate product data
        validateProductData(productDTO);
        
        // Create product entity
        Product product = new Product();
        product.setProductName(productDTO.getProductName());
        product.setPrice(productDTO.getPrice());
        product.setQuantity(productDTO.getQuantity());
        product.setReserved(productDTO.getReserved() != null ? productDTO.getReserved() : 0);
        product.setCustomerId(productDTO.getCustomerId());
        
        // Save product
        Product savedProduct = productRepository.save(product);
        
        return convertToDTO(savedProduct);
    }
    
    /**
     * Update Product - US008
     */
    public ProductDTO updateProduct(Long productId, ProductDTO productDTO) {
        Optional<Product> existingProductOpt = productRepository.findById(productId);
        if (existingProductOpt.isEmpty()) {
            throw new RuntimeException("Product not found with ID: " + productId);
        }
        
        Product existingProduct = existingProductOpt.get();
        
        // Validate product data
        validateProductData(productDTO);
        
        // Update product details
        if (productDTO.getProductName() != null && !productDTO.getProductName().trim().isEmpty()) {
            existingProduct.setProductName(productDTO.getProductName());
        }
        
        if (productDTO.getPrice() != null) {
            existingProduct.setPrice(productDTO.getPrice());
        }
        
        if (productDTO.getQuantity() != null) {
            existingProduct.setQuantity(productDTO.getQuantity());
        }
        
        if (productDTO.getReserved() != null) {
            existingProduct.setReserved(productDTO.getReserved());
        }
        
        if (productDTO.getCustomerId() != null) {
            existingProduct.setCustomerId(productDTO.getCustomerId());
        }
        
        // Save updated product
        Product updatedProduct = productRepository.save(existingProduct);
        
        return convertToDTO(updatedProduct);
    }
    
    /**
     * Delete Product - US009
     */
    public void deleteProduct(Long productId) {
        if (!productRepository.existsById(productId)) {
            throw new RuntimeException("Product not found with ID: " + productId);
        }
        
        productRepository.deleteById(productId);
    }
    
    /**
     * Search Product By Name - US006
     */
    public List<ProductDTO> searchProductByName(String productName) {
        if (productName == null || productName.trim().isEmpty()) {
            throw new RuntimeException("Product name cannot be empty");
        }
        
        List<Product> products = productRepository.findByNameIgnoreCase(productName.trim());
        
        if (products.isEmpty()) {
            throw new RuntimeException("Product not found");
        }
        
        return products.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Get product by ID
     */
    public ProductDTO getProductById(Long productId) {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            throw new RuntimeException("Product not found with ID: " + productId);
        }
        
        return convertToDTO(productOpt.get());
    }
    
    /**
     * Get all products
     */
    public List<ProductDTO> getAllProducts() {
        List<Product> products = productRepository.findAll();
        return products.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Update product quantity
     */
    public void updateProductQuantity(Long productId, Integer quantity) {
        if (!productRepository.existsById(productId)) {
            throw new RuntimeException("Product not found with ID: " + productId);
        }
        
        if (quantity < 0) {
            throw new RuntimeException("Quantity cannot be negative");
        }
        
        productRepository.updateQuantity(productId, quantity);
    }
    
    /**
     * Update product reserved quantity
     */
    public void updateProductReserved(Long productId, Integer reserved) {
        if (!productRepository.existsById(productId)) {
            throw new RuntimeException("Product not found with ID: " + productId);
        }
        
        if (reserved < 0) {
            throw new RuntimeException("Reserved quantity cannot be negative");
        }
        
        productRepository.updateReserved(productId, reserved);
    }
    
    /**
     * Check product availability
     */
    public boolean isProductAvailable(Long productId, Integer requestedQuantity) {
        Optional<Product> productOpt = productRepository.findById(productId);
        if (productOpt.isEmpty()) {
            return false;
        }
        
        Product product = productOpt.get();
        Integer availableQuantity = product.getQuantity() - (product.getReserved() != null ? product.getReserved() : 0);
        
        return availableQuantity >= requestedQuantity;
    }
    
    /**
     * Validate product data
     */
    private void validateProductData(ProductDTO productDTO) {
        if (productDTO.getQuantity() != null && productDTO.getQuantity() < 0) {
            throw new RuntimeException("Quantity cannot be negative");
        }
        
        if (productDTO.getPrice() != null && productDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Price must be greater than 0");
        }
        
        if (productDTO.getReserved() != null && productDTO.getReserved() < 0) {
            throw new RuntimeException("Reserved quantity cannot be negative");
        }
    }
    
    /**
     * Convert Product entity to DTO
     */
    private ProductDTO convertToDTO(Product product) {
        ProductDTO dto = new ProductDTO();
        dto.setProductId(product.getProductId());
        dto.setProductName(product.getProductName());
        dto.setPrice(product.getPrice());
        dto.setQuantity(product.getQuantity());
        dto.setReserved(product.getReserved());
        dto.setCustomerId(product.getCustomerId());
        dto.setCreatedAt(product.getCreatedAt());
        dto.setUpdatedAt(product.getUpdatedAt());
        return dto;
    }
}
