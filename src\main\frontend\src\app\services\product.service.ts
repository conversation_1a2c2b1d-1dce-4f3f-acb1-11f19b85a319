import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Product, ProductResponse } from '../models/product.model';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private apiUrl = '/api/products';

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  /**
   * Register Product - US007
   */
  registerProduct(product: Product): Observable<ProductResponse> {
    return this.http.post<ProductResponse>(`${this.apiUrl}/register`, product, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update Product - US008
   */
  updateProduct(productId: number, product: Product): Observable<ProductResponse> {
    return this.http.put<ProductResponse>(`${this.apiUrl}/${productId}`, product, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete Product - US009
   */
  deleteProduct(productId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${productId}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Search Product By Name - US006
   */
  searchProductByName(productName: string): Observable<ProductResponse[]> {
    return this.http.get<ProductResponse[]>(`${this.apiUrl}/search?name=${encodeURIComponent(productName)}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get product by ID
   */
  getProductById(productId: number): Observable<ProductResponse> {
    return this.http.get<ProductResponse>(`${this.apiUrl}/${productId}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all products
   */
  getAllProducts(): Observable<ProductResponse[]> {
    return this.http.get<ProductResponse[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update product quantity
   */
  updateProductQuantity(productId: number, quantity: number): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${productId}/quantity`, { quantity }, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Check product availability
   */
  checkProductAvailability(productId: number, requestedQuantity: number): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/${productId}/availability?quantity=${requestedQuantity}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 400) {
        errorMessage = error.error?.message || 'Bad request';
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    console.error('ProductService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
