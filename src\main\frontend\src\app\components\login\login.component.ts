import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CustomerService } from '../../services/customer.service';
import { LoginRequest } from '../../models/customer.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  template: `
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-6">
          <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
              <h3><i class="fas fa-sign-in-alt me-2"></i>Login</h3>
            </div>
            <div class="card-body">
              <!-- Success Message -->
              <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
                {{ successMessage }}
                <button type="button" class="btn-close" (click)="successMessage = ''"></button>
              </div>
              
              <!-- Error Message -->
              <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ errorMessage }}
                <button type="button" class="btn-close" (click)="errorMessage = ''"></button>
              </div>
              
              <form (ngSubmit)="onLogin()" #loginForm="ngForm">
                <div class="mb-3">
                  <label for="email" class="form-label">Email Address</label>
                  <input 
                    type="email" 
                    class="form-control" 
                    id="email" 
                    name="email"
                    [(ngModel)]="loginRequest.email"
                    required 
                    email
                    #email="ngModel"
                    placeholder="Enter your email">
                  <div *ngIf="email.invalid && email.touched" class="text-danger">
                    <small *ngIf="email.errors?.['required']">Email is required</small>
                    <small *ngIf="email.errors?.['email']">Please enter a valid email</small>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="password" class="form-label">Password</label>
                  <input 
                    type="password" 
                    class="form-control" 
                    id="password" 
                    name="password"
                    [(ngModel)]="loginRequest.password"
                    required 
                    minlength="8"
                    #password="ngModel"
                    placeholder="Enter your password">
                  <div *ngIf="password.invalid && password.touched" class="text-danger">
                    <small *ngIf="password.errors?.['required']">Password is required</small>
                    <small *ngIf="password.errors?.['minlength']">Password must be at least 8 characters</small>
                  </div>
                </div>
                
                <div class="d-grid gap-2">
                  <button 
                    type="submit" 
                    class="btn btn-primary"
                    [disabled]="loginForm.invalid || isLoading">
                    <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
                    <i *ngIf="!isLoading" class="fas fa-sign-in-alt me-2"></i>
                    {{ isLoading ? 'Logging in...' : 'Login' }}
                  </button>
                </div>
              </form>
              
              <hr>
              
              <div class="text-center">
                <p class="mb-0">Don't have an account? 
                  <a routerLink="/register" class="text-decoration-none">Register here</a>
                </p>
              </div>
              
              <div class="mt-3">
                <h6 class="text-muted">Demo Credentials:</h6>
                <small class="text-muted">
                  <strong>Admin:</strong> admin / admin123<br>
                  <strong>Customer:</strong> <EMAIL> / Password123!
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .card {
      border: none;
      border-radius: 15px;
    }
    
    .card-header {
      border-radius: 15px 15px 0 0 !important;
    }
    
    .form-control {
      border-radius: 10px;
    }
    
    .btn {
      border-radius: 10px;
    }
    
    .alert {
      border-radius: 10px;
    }
  `]
})
export class LoginComponent {
  loginRequest: LoginRequest = {
    email: '',
    password: ''
  };
  
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private customerService: CustomerService,
    private router: Router
  ) {}

  onLogin(): void {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.customerService.login(this.loginRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.successMessage = 'Login successful! Redirecting...';
          
          // Redirect based on role
          setTimeout(() => {
            if (response.role === 'ADMIN') {
              this.router.navigate(['/admin-dashboard']);
            } else {
              this.router.navigate(['/customer-dashboard']);
            }
          }, 1500);
        } else {
          this.errorMessage = response.message || 'Login failed';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Please Enter Correct UserName and Password';
      }
    });
  }
}
