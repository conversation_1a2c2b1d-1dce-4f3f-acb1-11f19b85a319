package com.grocery.controller.api;

import com.grocery.dto.ProductDTO;
import com.grocery.security.SqlInjectionPreventionUtil;
import com.grocery.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/products")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ProductApiController {
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private SqlInjectionPreventionUtil sqlInjectionPreventionUtil;
    
    /**
     * Register Product API - US007
     */
    @PostMapping("/register")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> registerProduct(@Valid @RequestBody ProductDTO productDTO,
                                            BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input for SQL injection
            if (!sqlInjectionPreventionUtil.validateInput(productDTO.getProductName(), 100)) {
                response.put("success", false);
                response.put("message", "Invalid product name detected.");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            ProductDTO savedProduct = productService.productRegistration(productDTO);
            
            response.put("success", true);
            response.put("message", "Product registered successfully");
            response.put("productId", savedProduct.getProductId());
            response.put("product", savedProduct);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Update Product API - US008
     */
    @PutMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateProduct(@PathVariable Long productId,
                                          @Valid @RequestBody ProductDTO productDTO,
                                          BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input for SQL injection
            if (!sqlInjectionPreventionUtil.validateInput(productDTO.getProductName(), 100)) {
                response.put("success", false);
                response.put("message", "Invalid product name detected.");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            ProductDTO updatedProduct = productService.updateProduct(productId, productDTO);
            
            response.put("success", true);
            response.put("message", "Product updated successfully");
            response.put("product", updatedProduct);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Delete Product API - US009
     */
    @DeleteMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteProduct(@PathVariable Long productId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            productService.deleteProduct(productId);
            
            response.put("success", true);
            response.put("message", "Product deleted successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Search Product By Name API - US006
     */
    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ADMIN', 'CUSTOMER')")
    public ResponseEntity<?> searchProductByName(@RequestParam String name) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // SQL Injection Prevention
            String sanitizedName = sqlInjectionPreventionUtil.validateAndSanitizeSearchInput(name);
            if (sanitizedName == null) {
                response.put("success", false);
                response.put("message", "Invalid search input detected.");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<ProductDTO> products = productService.searchProductByName(sanitizedName);
            
            response.put("success", true);
            response.put("message", "Search completed successfully");
            response.put("products", products);
            response.put("count", products.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get Product by ID API
     */
    @GetMapping("/{productId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'CUSTOMER')")
    public ResponseEntity<?> getProductById(@PathVariable Long productId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            ProductDTO product = productService.getProductById(productId);
            
            response.put("success", true);
            response.put("product", product);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get All Products API
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'CUSTOMER')")
    public ResponseEntity<?> getAllProducts() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<ProductDTO> products = productService.getAllProducts();
            
            response.put("success", true);
            response.put("products", products);
            response.put("count", products.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Update Product Quantity API
     */
    @PatchMapping("/{productId}/quantity")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateProductQuantity(@PathVariable Long productId,
                                                  @RequestBody Map<String, Integer> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Integer quantity = request.get("quantity");
            if (quantity == null) {
                response.put("success", false);
                response.put("message", "Quantity is required");
                return ResponseEntity.badRequest().body(response);
            }
            
            productService.updateProductQuantity(productId, quantity);
            
            response.put("success", true);
            response.put("message", "Product quantity updated successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Check Product Availability API
     */
    @GetMapping("/{productId}/availability")
    @PreAuthorize("hasAnyRole('ADMIN', 'CUSTOMER')")
    public ResponseEntity<?> checkProductAvailability(@PathVariable Long productId,
                                                     @RequestParam Integer quantity) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isAvailable = productService.isProductAvailable(productId, quantity);
            
            response.put("success", true);
            response.put("available", isAvailable);
            response.put("message", isAvailable ? "Product is available" : "Insufficient stock");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
