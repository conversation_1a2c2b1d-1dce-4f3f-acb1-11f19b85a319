package com.grocery.repository;

import com.grocery.model.Admin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class AdminRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String INSERT_ADMIN = 
        "INSERT INTO admins (username, password, full_name, email, role, is_active, created_at, updated_at) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_ADMIN = 
        "UPDATE admins SET username = ?, password = ?, full_name = ?, email = ?, role = ?, is_active = ?, updated_at = ? " +
        "WHERE admin_id = ?";
    
    private static final String FIND_BY_ID = 
        "SELECT * FROM admins WHERE admin_id = ?";
    
    private static final String FIND_BY_USERNAME = 
        "SELECT * FROM admins WHERE username = ?";
    
    private static final String FIND_ALL = 
        "SELECT * FROM admins ORDER BY created_at DESC";
    
    private static final String DELETE_BY_ID = 
        "DELETE FROM admins WHERE admin_id = ?";
    
    private static final String EXISTS_BY_USERNAME = 
        "SELECT COUNT(*) FROM admins WHERE username = ?";
    
    private static final String UPDATE_LAST_LOGIN = 
        "UPDATE admins SET last_login = ?, updated_at = ? WHERE admin_id = ?";
    
    private final RowMapper<Admin> adminRowMapper = new AdminRowMapper();
    
    public Admin save(Admin admin) {
        LocalDateTime now = LocalDateTime.now();
        
        if (admin.getAdminId() == null) {
            // Insert new admin
            KeyHolder keyHolder = new GeneratedKeyHolder();
            
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(INSERT_ADMIN, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, admin.getUsername());
                ps.setString(2, admin.getPassword());
                ps.setString(3, admin.getFullName());
                ps.setString(4, admin.getEmail());
                ps.setString(5, admin.getRole().name());
                ps.setBoolean(6, admin.getIsActive());
                ps.setObject(7, now);
                ps.setObject(8, now);
                return ps;
            }, keyHolder);
            
            admin.setAdminId(keyHolder.getKey().longValue());
            admin.setCreatedAt(now);
            admin.setUpdatedAt(now);
        } else {
            // Update existing admin
            admin.setUpdatedAt(now);
            jdbcTemplate.update(UPDATE_ADMIN,
                admin.getUsername(),
                admin.getPassword(),
                admin.getFullName(),
                admin.getEmail(),
                admin.getRole().name(),
                admin.getIsActive(),
                admin.getUpdatedAt(),
                admin.getAdminId()
            );
        }
        
        return admin;
    }
    
    public Optional<Admin> findById(Long id) {
        try {
            Admin admin = jdbcTemplate.queryForObject(FIND_BY_ID, adminRowMapper, id);
            return Optional.ofNullable(admin);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public Optional<Admin> findByUsername(String username) {
        try {
            Admin admin = jdbcTemplate.queryForObject(FIND_BY_USERNAME, adminRowMapper, username);
            return Optional.ofNullable(admin);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public List<Admin> findAll() {
        return jdbcTemplate.query(FIND_ALL, adminRowMapper);
    }
    
    public void deleteById(Long id) {
        jdbcTemplate.update(DELETE_BY_ID, id);
    }
    
    public boolean existsByUsername(String username) {
        Integer count = jdbcTemplate.queryForObject(EXISTS_BY_USERNAME, Integer.class, username);
        return count != null && count > 0;
    }
    
    public void updateLastLogin(Long adminId) {
        LocalDateTime now = LocalDateTime.now();
        jdbcTemplate.update(UPDATE_LAST_LOGIN, now, now, adminId);
    }
    
    private static class AdminRowMapper implements RowMapper<Admin> {
        @Override
        public Admin mapRow(ResultSet rs, int rowNum) throws SQLException {
            Admin admin = new Admin();
            admin.setAdminId(rs.getLong("admin_id"));
            admin.setUsername(rs.getString("username"));
            admin.setPassword(rs.getString("password"));
            admin.setFullName(rs.getString("full_name"));
            admin.setEmail(rs.getString("email"));
            admin.setRole(Admin.AdminRole.valueOf(rs.getString("role")));
            admin.setIsActive(rs.getBoolean("is_active"));
            admin.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
            admin.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
            
            if (rs.getTimestamp("last_login") != null) {
                admin.setLastLogin(rs.getTimestamp("last_login").toLocalDateTime());
            }
            
            return admin;
        }
    }
}
