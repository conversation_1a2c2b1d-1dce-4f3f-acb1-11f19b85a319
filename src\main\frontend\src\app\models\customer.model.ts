export interface Customer {
  customerId?: number;
  fullName: string;
  email: string;
  password?: string;
  address: string;
  contactNumber: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CustomerResponse {
  customerId: number;
  fullName: string;
  email: string;
  address: string;
  contactNumber: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  user?: CustomerResponse;
  role?: string;
}
