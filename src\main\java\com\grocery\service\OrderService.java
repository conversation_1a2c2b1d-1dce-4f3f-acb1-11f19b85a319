package com.grocery.service;

import com.grocery.dto.OrderDTO;
import com.grocery.model.Customer;
import com.grocery.model.Order;
import com.grocery.repository.CustomerRepository;
import com.grocery.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class OrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    /**
     * Get Customer Order Details - US004
     */
    public List<OrderDTO> getCustomerOrderDetails(Long customerId) {
        // Verify customer exists
        Optional<Customer> customerOpt = customerRepository.findById(customerId);
        if (customerOpt.isEmpty()) {
            throw new RuntimeException("Customer not found with ID: " + customerId);
        }
        
        // Get customer orders
        List<Order> orders = orderRepository.findByCustomerId(customerId);
        
        if (orders.isEmpty()) {
            throw new RuntimeException("No orders found for customer ID: " + customerId);
        }
        
        return orders.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Create new order
     */
    public OrderDTO createOrder(OrderDTO orderDTO) {
        // Verify customer exists
        Optional<Customer> customerOpt = customerRepository.findById(orderDTO.getCustomerId());
        if (customerOpt.isEmpty()) {
            throw new RuntimeException("Customer not found with ID: " + orderDTO.getCustomerId());
        }
        
        Customer customer = customerOpt.get();
        
        // Create order entity
        Order order = new Order();
        order.setCustomer(customer);
        order.setOrderDate(orderDTO.getOrderDate());
        order.setOrderAmount(orderDTO.getOrderAmount());
        
        if (orderDTO.getStatus() != null) {
            order.setStatus(Order.OrderStatus.valueOf(orderDTO.getStatus()));
        }
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        return convertToDTO(savedOrder);
    }
    
    /**
     * Update order
     */
    public OrderDTO updateOrder(Long orderId, OrderDTO orderDTO) {
        Optional<Order> existingOrderOpt = orderRepository.findById(orderId);
        if (existingOrderOpt.isEmpty()) {
            throw new RuntimeException("Order not found with ID: " + orderId);
        }
        
        Order existingOrder = existingOrderOpt.get();
        
        // Update order details
        if (orderDTO.getOrderAmount() != null) {
            existingOrder.setOrderAmount(orderDTO.getOrderAmount());
        }
        
        if (orderDTO.getStatus() != null) {
            existingOrder.setStatus(Order.OrderStatus.valueOf(orderDTO.getStatus()));
        }
        
        if (orderDTO.getOrderDate() != null) {
            existingOrder.setOrderDate(orderDTO.getOrderDate());
        }
        
        // Save updated order
        Order updatedOrder = orderRepository.save(existingOrder);
        
        return convertToDTO(updatedOrder);
    }
    
    /**
     * Get order by ID
     */
    public OrderDTO getOrderById(Long orderId) {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isEmpty()) {
            throw new RuntimeException("Order not found with ID: " + orderId);
        }
        
        return convertToDTO(orderOpt.get());
    }
    
    /**
     * Get all orders
     */
    public List<OrderDTO> getAllOrders() {
        List<Order> orders = orderRepository.findAll();
        return orders.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Delete order
     */
    public void deleteOrder(Long orderId) {
        if (!orderRepository.findById(orderId).isPresent()) {
            throw new RuntimeException("Order not found with ID: " + orderId);
        }
        
        orderRepository.deleteById(orderId);
    }
    
    /**
     * Update order status
     */
    public OrderDTO updateOrderStatus(Long orderId, String status) {
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (orderOpt.isEmpty()) {
            throw new RuntimeException("Order not found with ID: " + orderId);
        }
        
        Order order = orderOpt.get();
        
        try {
            order.setStatus(Order.OrderStatus.valueOf(status.toUpperCase()));
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid order status: " + status);
        }
        
        Order updatedOrder = orderRepository.save(order);
        return convertToDTO(updatedOrder);
    }
    
    /**
     * Convert Order entity to DTO
     */
    private OrderDTO convertToDTO(Order order) {
        OrderDTO dto = new OrderDTO();
        dto.setOrderId(order.getOrderId());
        dto.setCustomerId(order.getCustomer().getCustomerId());
        dto.setCustomerName(order.getCustomer().getFullName());
        dto.setCustomerEmail(order.getCustomer().getEmail());
        dto.setOrderDate(order.getOrderDate());
        dto.setOrderAmount(order.getOrderAmount());
        dto.setStatus(order.getStatus().name());
        dto.setCreatedAt(order.getCreatedAt());
        dto.setUpdatedAt(order.getUpdatedAt());
        return dto;
    }
}
