package com.grocery.controller;

import com.grocery.dto.CustomerDTO;
import com.grocery.dto.ProductDTO;
import com.grocery.dto.OrderDTO;
import com.grocery.security.SqlInjectionPreventionUtil;
import com.grocery.service.CustomerService;
import com.grocery.service.ProductService;
import com.grocery.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private SqlInjectionPreventionUtil sqlInjectionPreventionUtil;
    
    /**
     * Admin menu - US001
     */
    @GetMapping("/menu")
    public String adminMenu() {
        return "admin/menu";
    }
    
    /**
     * Customer Registration - Option 1
     */
    @GetMapping("/customer/register")
    public String customerRegistrationForm(Model model) {
        model.addAttribute("customerDTO", new CustomerDTO());
        return "admin/customer-register";
    }
    
    @PostMapping("/customer/register")
    public String customerRegistration(@Valid @ModelAttribute CustomerDTO customerDTO,
                                     BindingResult bindingResult,
                                     Model model,
                                     RedirectAttributes redirectAttributes) {
        
        // SQL Injection Prevention
        if (!sqlInjectionPreventionUtil.validateInput(customerDTO.getFullName(), 100) ||
            !sqlInjectionPreventionUtil.isEmailSafe(customerDTO.getEmail()) ||
            !sqlInjectionPreventionUtil.validateInput(customerDTO.getAddress(), 255) ||
            !sqlInjectionPreventionUtil.isPhoneNumberSafe(customerDTO.getContactNumber())) {
            
            model.addAttribute("error", "Invalid input detected. Please check your data.");
            return "admin/customer-register";
        }
        
        if (bindingResult.hasErrors()) {
            return "admin/customer-register";
        }
        
        try {
            CustomerDTO savedCustomer = customerService.customerRegistration(customerDTO);
            redirectAttributes.addFlashAttribute("success", 
                "Customer registered successfully! Customer ID: " + savedCustomer.getCustomerId());
            return "redirect:/admin/menu";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/customer-register";
        }
    }
    
    /**
     * Update Customer Details - Option 2
     */
    @GetMapping("/customer/update")
    public String customerUpdateForm(Model model) {
        model.addAttribute("customers", customerService.getAllCustomers());
        return "admin/customer-update";
    }
    
    @GetMapping("/customer/update/{id}")
    public String customerUpdateForm(@PathVariable Long id, Model model) {
        try {
            CustomerDTO customer = customerService.getCustomerById(id);
            model.addAttribute("customerDTO", customer);
            return "admin/customer-update-form";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/customer-update";
        }
    }
    
    @PostMapping("/customer/update/{id}")
    public String customerUpdate(@PathVariable Long id,
                               @Valid @ModelAttribute CustomerDTO customerDTO,
                               BindingResult bindingResult,
                               Model model,
                               RedirectAttributes redirectAttributes) {
        
        // SQL Injection Prevention
        if (!sqlInjectionPreventionUtil.validateInput(customerDTO.getFullName(), 100) ||
            !sqlInjectionPreventionUtil.isEmailSafe(customerDTO.getEmail()) ||
            !sqlInjectionPreventionUtil.validateInput(customerDTO.getAddress(), 255) ||
            !sqlInjectionPreventionUtil.isPhoneNumberSafe(customerDTO.getContactNumber())) {
            
            model.addAttribute("error", "Invalid input detected. Please check your data.");
            return "admin/customer-update-form";
        }
        
        if (bindingResult.hasErrors()) {
            return "admin/customer-update-form";
        }
        
        try {
            customerService.customerUpdate(id, customerDTO);
            redirectAttributes.addFlashAttribute("success", "Customer updated successfully!");
            return "redirect:/admin/menu";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/customer-update-form";
        }
    }
    
    /**
     * Get Customer Order Details - Option 3
     */
    @GetMapping("/customer/orders")
    public String customerOrdersForm(Model model) {
        model.addAttribute("customers", customerService.getAllCustomers());
        return "admin/customer-orders";
    }
    
    @GetMapping("/customer/orders/{customerId}")
    public String getCustomerOrders(@PathVariable Long customerId, Model model) {
        try {
            List<OrderDTO> orders = orderService.getCustomerOrderDetails(customerId);
            CustomerDTO customer = customerService.getCustomerById(customerId);
            model.addAttribute("orders", orders);
            model.addAttribute("customer", customer);
            return "admin/customer-orders-details";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/customer-orders";
        }
    }
    
    /**
     * Customer Search - Option 4
     */
    @GetMapping("/customer/search")
    public String customerSearchForm() {
        return "admin/customer-search";
    }
    
    @PostMapping("/customer/search")
    public String customerSearch(@RequestParam String customerName, Model model) {
        // SQL Injection Prevention
        String sanitizedName = sqlInjectionPreventionUtil.validateAndSanitizeSearchInput(customerName);
        if (sanitizedName == null) {
            model.addAttribute("error", "Invalid search input detected.");
            return "admin/customer-search";
        }
        
        try {
            List<CustomerDTO> customers = customerService.searchCustomerByName(sanitizedName);
            model.addAttribute("customers", customers);
            model.addAttribute("searchTerm", sanitizedName);
            return "admin/customer-search-results";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/customer-search";
        }
    }
    
    /**
     * Product Search - Option 5
     */
    @GetMapping("/product/search")
    public String productSearchForm() {
        return "admin/product-search";
    }
    
    @PostMapping("/product/search")
    public String productSearch(@RequestParam String productName, Model model) {
        // SQL Injection Prevention
        String sanitizedName = sqlInjectionPreventionUtil.validateAndSanitizeSearchInput(productName);
        if (sanitizedName == null) {
            model.addAttribute("error", "Invalid search input detected.");
            return "admin/product-search";
        }
        
        try {
            List<ProductDTO> products = productService.searchProductByName(sanitizedName);
            model.addAttribute("products", products);
            model.addAttribute("searchTerm", sanitizedName);
            return "admin/product-search-results";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/product-search";
        }
    }
    
    /**
     * Register Product - Option 6
     */
    @GetMapping("/product/register")
    public String productRegistrationForm(Model model) {
        model.addAttribute("productDTO", new ProductDTO());
        return "admin/product-register";
    }
    
    @PostMapping("/product/register")
    public String productRegistration(@Valid @ModelAttribute ProductDTO productDTO,
                                    BindingResult bindingResult,
                                    Model model,
                                    RedirectAttributes redirectAttributes) {
        
        // SQL Injection Prevention
        if (!sqlInjectionPreventionUtil.validateInput(productDTO.getProductName(), 100)) {
            model.addAttribute("error", "Invalid product name detected.");
            return "admin/product-register";
        }
        
        if (bindingResult.hasErrors()) {
            return "admin/product-register";
        }
        
        try {
            ProductDTO savedProduct = productService.productRegistration(productDTO);
            redirectAttributes.addFlashAttribute("success", 
                "Product registered successfully! Product ID: " + savedProduct.getProductId());
            return "redirect:/admin/menu";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/product-register";
        }
    }
    
    /**
     * Update Product - Option 7
     */
    @GetMapping("/product/update")
    public String productUpdateForm(Model model) {
        model.addAttribute("products", productService.getAllProducts());
        return "admin/product-update";
    }
    
    @GetMapping("/product/update/{id}")
    public String productUpdateForm(@PathVariable Long id, Model model) {
        try {
            ProductDTO product = productService.getProductById(id);
            model.addAttribute("productDTO", product);
            return "admin/product-update-form";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/product-update";
        }
    }
    
    @PostMapping("/product/update/{id}")
    public String productUpdate(@PathVariable Long id,
                              @Valid @ModelAttribute ProductDTO productDTO,
                              BindingResult bindingResult,
                              Model model,
                              RedirectAttributes redirectAttributes) {
        
        // SQL Injection Prevention
        if (!sqlInjectionPreventionUtil.validateInput(productDTO.getProductName(), 100)) {
            model.addAttribute("error", "Invalid product name detected.");
            return "admin/product-update-form";
        }
        
        if (bindingResult.hasErrors()) {
            return "admin/product-update-form";
        }
        
        try {
            productService.updateProduct(id, productDTO);
            redirectAttributes.addFlashAttribute("success", "Product updated successfully!");
            return "redirect:/admin/menu";
        } catch (Exception e) {
            model.addAttribute("error", e.getMessage());
            return "admin/product-update-form";
        }
    }
    
    /**
     * Delete Product - Option 8
     */
    @GetMapping("/product/delete")
    public String productDeleteForm(Model model) {
        model.addAttribute("products", productService.getAllProducts());
        return "admin/product-delete";
    }
    
    @PostMapping("/product/delete/{id}")
    public String productDelete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            productService.deleteProduct(id);
            redirectAttributes.addFlashAttribute("success", "Product deleted successfully!");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
        }
        return "redirect:/admin/menu";
    }
}
