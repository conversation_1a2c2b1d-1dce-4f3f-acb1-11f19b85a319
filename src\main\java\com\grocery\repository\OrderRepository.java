package com.grocery.repository;

import com.grocery.model.Order;
import com.grocery.model.Customer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class OrderRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String INSERT_ORDER = 
        "INSERT INTO orders (customer_id, order_date, order_amount, status, created_at, updated_at) " +
        "VALUES (?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_ORDER = 
        "UPDATE orders SET customer_id = ?, order_date = ?, order_amount = ?, status = ?, updated_at = ? " +
        "WHERE order_id = ?";
    
    private static final String FIND_BY_ID = 
        "SELECT o.*, c.full_name, c.email FROM orders o " +
        "JOIN customers c ON o.customer_id = c.customer_id " +
        "WHERE o.order_id = ?";
    
    private static final String FIND_ALL = 
        "SELECT o.*, c.full_name, c.email FROM orders o " +
        "JOIN customers c ON o.customer_id = c.customer_id " +
        "ORDER BY o.order_date DESC";
    
    private static final String FIND_BY_CUSTOMER_ID = 
        "SELECT o.*, c.full_name, c.email FROM orders o " +
        "JOIN customers c ON o.customer_id = c.customer_id " +
        "WHERE o.customer_id = ? ORDER BY o.order_date DESC";
    
    private static final String DELETE_BY_ID = 
        "DELETE FROM orders WHERE order_id = ?";
    
    private final RowMapper<Order> orderRowMapper = new OrderRowMapper();
    
    public Order save(Order order) {
        LocalDateTime now = LocalDateTime.now();
        
        if (order.getOrderId() == null) {
            // Insert new order
            KeyHolder keyHolder = new GeneratedKeyHolder();
            
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(INSERT_ORDER, Statement.RETURN_GENERATED_KEYS);
                ps.setLong(1, order.getCustomer().getCustomerId());
                ps.setObject(2, order.getOrderDate());
                ps.setBigDecimal(3, order.getOrderAmount());
                ps.setString(4, order.getStatus().name());
                ps.setObject(5, now);
                ps.setObject(6, now);
                return ps;
            }, keyHolder);
            
            order.setOrderId(keyHolder.getKey().longValue());
            order.setCreatedAt(now);
            order.setUpdatedAt(now);
        } else {
            // Update existing order
            order.setUpdatedAt(now);
            jdbcTemplate.update(UPDATE_ORDER,
                order.getCustomer().getCustomerId(),
                order.getOrderDate(),
                order.getOrderAmount(),
                order.getStatus().name(),
                order.getUpdatedAt(),
                order.getOrderId()
            );
        }
        
        return order;
    }
    
    public Optional<Order> findById(Long id) {
        try {
            Order order = jdbcTemplate.queryForObject(FIND_BY_ID, orderRowMapper, id);
            return Optional.ofNullable(order);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public List<Order> findAll() {
        return jdbcTemplate.query(FIND_ALL, orderRowMapper);
    }
    
    public List<Order> findByCustomerId(Long customerId) {
        return jdbcTemplate.query(FIND_BY_CUSTOMER_ID, orderRowMapper, customerId);
    }
    
    public void deleteById(Long id) {
        jdbcTemplate.update(DELETE_BY_ID, id);
    }
    
    private static class OrderRowMapper implements RowMapper<Order> {
        @Override
        public Order mapRow(ResultSet rs, int rowNum) throws SQLException {
            Order order = new Order();
            order.setOrderId(rs.getLong("order_id"));
            order.setOrderDate(rs.getTimestamp("order_date").toLocalDateTime());
            order.setOrderAmount(rs.getBigDecimal("order_amount"));
            order.setStatus(Order.OrderStatus.valueOf(rs.getString("status")));
            order.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
            order.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
            
            // Create customer object with basic info
            Customer customer = new Customer();
            customer.setCustomerId(rs.getLong("customer_id"));
            customer.setFullName(rs.getString("full_name"));
            customer.setEmail(rs.getString("email"));
            order.setCustomer(customer);
            
            return order;
        }
    }
}
