export interface Product {
  productId?: number;
  productName: string;
  price: number;
  quantity: number;
  reserved?: number;
  customerId?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProductResponse {
  productId: number;
  productName: string;
  price: number;
  quantity: number;
  reserved: number;
  customerId: number;
  createdAt: Date;
  updatedAt: Date;
  availableQuantity?: number;
}
