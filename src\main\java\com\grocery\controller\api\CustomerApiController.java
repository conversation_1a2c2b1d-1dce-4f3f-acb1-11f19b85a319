package com.grocery.controller.api;

import com.grocery.dto.CustomerDTO;
import com.grocery.security.SqlInjectionPreventionUtil;
import com.grocery.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/customers")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CustomerApiController {
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private SqlInjectionPreventionUtil sqlInjectionPreventionUtil;
    
    /**
     * Customer Registration API - US002
     */
    @PostMapping("/register")
    public ResponseEntity<?> registerCustomer(@Valid @RequestBody CustomerDTO customerDTO,
                                             BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input for SQL injection
            if (!sqlInjectionPreventionUtil.validateInput(customerDTO.getFullName(), 100) ||
                !sqlInjectionPreventionUtil.isEmailSafe(customerDTO.getEmail()) ||
                !sqlInjectionPreventionUtil.validateInput(customerDTO.getAddress(), 255) ||
                !sqlInjectionPreventionUtil.isPhoneNumberSafe(customerDTO.getContactNumber())) {
                
                response.put("success", false);
                response.put("message", "Invalid input detected. Please check your data.");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            CustomerDTO savedCustomer = customerService.customerRegistration(customerDTO);
            
            response.put("success", true);
            response.put("message", "Customer registered successfully");
            response.put("customerId", savedCustomer.getCustomerId());
            response.put("customer", savedCustomer);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Update Customer Details API - US003
     */
    @PutMapping("/{customerId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal.customerId)")
    public ResponseEntity<?> updateCustomer(@PathVariable Long customerId,
                                           @Valid @RequestBody CustomerDTO customerDTO,
                                           BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input for SQL injection
            if (!sqlInjectionPreventionUtil.validateInput(customerDTO.getFullName(), 100) ||
                !sqlInjectionPreventionUtil.isEmailSafe(customerDTO.getEmail()) ||
                !sqlInjectionPreventionUtil.validateInput(customerDTO.getAddress(), 255) ||
                !sqlInjectionPreventionUtil.isPhoneNumberSafe(customerDTO.getContactNumber())) {
                
                response.put("success", false);
                response.put("message", "Invalid input detected. Please check your data.");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            CustomerDTO updatedCustomer = customerService.customerUpdate(customerId, customerDTO);
            
            response.put("success", true);
            response.put("message", "Customer updated successfully");
            response.put("customer", updatedCustomer);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Search Customer By Name API - US005
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> searchCustomerByName(@RequestParam String name) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // SQL Injection Prevention
            String sanitizedName = sqlInjectionPreventionUtil.validateAndSanitizeSearchInput(name);
            if (sanitizedName == null) {
                response.put("success", false);
                response.put("message", "Invalid search input detected.");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<CustomerDTO> customers = customerService.searchCustomerByName(sanitizedName);
            
            response.put("success", true);
            response.put("message", "Search completed successfully");
            response.put("customers", customers);
            response.put("count", customers.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get Customer by ID API
     */
    @GetMapping("/{customerId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal.customerId)")
    public ResponseEntity<?> getCustomerById(@PathVariable Long customerId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            CustomerDTO customer = customerService.getCustomerById(customerId);
            
            response.put("success", true);
            response.put("customer", customer);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get All Customers API
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllCustomers() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<CustomerDTO> customers = customerService.getAllCustomers();
            
            response.put("success", true);
            response.put("customers", customers);
            response.put("count", customers.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Delete Customer API
     */
    @DeleteMapping("/{customerId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteCustomer(@PathVariable Long customerId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            customerService.deleteCustomer(customerId);
            
            response.put("success", true);
            response.put("message", "Customer deleted successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
