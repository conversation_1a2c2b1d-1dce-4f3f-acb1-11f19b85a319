package com.grocery.controller.api;

import com.grocery.dto.OrderDTO;
import com.grocery.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/orders")
@CrossOrigin(origins = "*", maxAge = 3600)
public class OrderApiController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * Get Customer Order Details API - US004
     */
    @GetMapping("/customer/{customerId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal.customerId)")
    public ResponseEntity<?> getCustomerOrderDetails(@PathVariable Long customerId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<OrderDTO> orders = orderService.getCustomerOrderDetails(customerId);
            
            response.put("success", true);
            response.put("message", "Customer orders retrieved successfully");
            response.put("orders", orders);
            response.put("count", orders.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Create Order API
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'CUSTOMER')")
    public ResponseEntity<?> createOrder(@Valid @RequestBody OrderDTO orderDTO,
                                        BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            OrderDTO savedOrder = orderService.createOrder(orderDTO);
            
            response.put("success", true);
            response.put("message", "Order created successfully");
            response.put("orderId", savedOrder.getOrderId());
            response.put("order", savedOrder);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Update Order API
     */
    @PutMapping("/{orderId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateOrder(@PathVariable Long orderId,
                                        @Valid @RequestBody OrderDTO orderDTO,
                                        BindingResult bindingResult) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (bindingResult.hasErrors()) {
                response.put("success", false);
                response.put("message", "Validation failed");
                response.put("errors", bindingResult.getAllErrors());
                return ResponseEntity.badRequest().body(response);
            }
            
            OrderDTO updatedOrder = orderService.updateOrder(orderId, orderDTO);
            
            response.put("success", true);
            response.put("message", "Order updated successfully");
            response.put("order", updatedOrder);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get Order by ID API
     */
    @GetMapping("/{orderId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and @orderService.getOrderById(#orderId).customerId == authentication.principal.customerId)")
    public ResponseEntity<?> getOrderById(@PathVariable Long orderId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            OrderDTO order = orderService.getOrderById(orderId);
            
            response.put("success", true);
            response.put("order", order);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Get All Orders API
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllOrders() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<OrderDTO> orders = orderService.getAllOrders();
            
            response.put("success", true);
            response.put("orders", orders);
            response.put("count", orders.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Delete Order API
     */
    @DeleteMapping("/{orderId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteOrder(@PathVariable Long orderId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            orderService.deleteOrder(orderId);
            
            response.put("success", true);
            response.put("message", "Order deleted successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Update Order Status API
     */
    @PatchMapping("/{orderId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateOrderStatus(@PathVariable Long orderId,
                                              @RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String status = request.get("status");
            if (status == null || status.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "Status is required");
                return ResponseEntity.badRequest().body(response);
            }
            
            OrderDTO updatedOrder = orderService.updateOrderStatus(orderId, status);
            
            response.put("success", true);
            response.put("message", "Order status updated successfully");
            response.put("order", updatedOrder);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
