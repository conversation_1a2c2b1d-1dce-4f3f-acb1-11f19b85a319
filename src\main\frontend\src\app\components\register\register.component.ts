import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CustomerService } from '../../services/customer.service';
import { Customer } from '../../models/customer.model';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  template: `
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
              <h3><i class="fas fa-user-plus me-2"></i>Customer Registration</h3>
            </div>
            <div class="card-body">
              <!-- Success Message -->
              <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
                {{ successMessage }}
                <button type="button" class="btn-close" (click)="successMessage = ''"></button>
              </div>
              
              <!-- Error Message -->
              <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ errorMessage }}
                <button type="button" class="btn-close" (click)="errorMessage = ''"></button>
              </div>
              
              <form (ngSubmit)="onRegister()" #registerForm="ngForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="fullName" class="form-label">Full Name *</label>
                      <input 
                        type="text" 
                        class="form-control" 
                        id="fullName" 
                        name="fullName"
                        [(ngModel)]="customer.fullName"
                        required 
                        minlength="2"
                        maxlength="100"
                        #fullName="ngModel"
                        placeholder="Enter your full name">
                      <div *ngIf="fullName.invalid && fullName.touched" class="text-danger">
                        <small *ngIf="fullName.errors?.['required']">Full name is required</small>
                        <small *ngIf="fullName.errors?.['minlength']">Full name must be at least 2 characters</small>
                        <small *ngIf="fullName.errors?.['maxlength']">Full name cannot exceed 100 characters</small>
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="email" class="form-label">Email Address *</label>
                      <input 
                        type="email" 
                        class="form-control" 
                        id="email" 
                        name="email"
                        [(ngModel)]="customer.email"
                        required 
                        email
                        #email="ngModel"
                        placeholder="Enter your email">
                      <div *ngIf="email.invalid && email.touched" class="text-danger">
                        <small *ngIf="email.errors?.['required']">Email is required</small>
                        <small *ngIf="email.errors?.['email']">Please enter a valid email</small>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="password" class="form-label">Password *</label>
                      <input 
                        type="password" 
                        class="form-control" 
                        id="password" 
                        name="password"
                        [(ngModel)]="customer.password"
                        required 
                        pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"
                        #password="ngModel"
                        placeholder="Enter your password">
                      <div *ngIf="password.invalid && password.touched" class="text-danger">
                        <small *ngIf="password.errors?.['required']">Password is required</small>
                        <small *ngIf="password.errors?.['pattern']">
                          Password must contain at least one uppercase letter, one lowercase letter, one digit and one special character
                        </small>
                      </div>
                      <small class="text-muted">
                        Password must be at least 8 characters with uppercase, lowercase, digit, and special character
                      </small>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="contactNumber" class="form-label">Contact Number *</label>
                      <input 
                        type="tel" 
                        class="form-control" 
                        id="contactNumber" 
                        name="contactNumber"
                        [(ngModel)]="customer.contactNumber"
                        required 
                        pattern="^\\d{10}$"
                        #contactNumber="ngModel"
                        placeholder="Enter 10-digit contact number">
                      <div *ngIf="contactNumber.invalid && contactNumber.touched" class="text-danger">
                        <small *ngIf="contactNumber.errors?.['required']">Contact number is required</small>
                        <small *ngIf="contactNumber.errors?.['pattern']">Contact number must be exactly 10 digits</small>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="address" class="form-label">Address *</label>
                  <textarea 
                    class="form-control" 
                    id="address" 
                    name="address"
                    [(ngModel)]="customer.address"
                    required 
                    minlength="10"
                    maxlength="255"
                    rows="3"
                    #address="ngModel"
                    placeholder="Enter your complete address"></textarea>
                  <div *ngIf="address.invalid && address.touched" class="text-danger">
                    <small *ngIf="address.errors?.['required']">Address is required</small>
                    <small *ngIf="address.errors?.['minlength']">Address must be at least 10 characters</small>
                    <small *ngIf="address.errors?.['maxlength']">Address cannot exceed 255 characters</small>
                  </div>
                </div>
                
                <div class="d-grid gap-2">
                  <button 
                    type="submit" 
                    class="btn btn-success"
                    [disabled]="registerForm.invalid || isLoading">
                    <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
                    <i *ngIf="!isLoading" class="fas fa-user-plus me-2"></i>
                    {{ isLoading ? 'Registering...' : 'Register' }}
                  </button>
                </div>
              </form>
              
              <hr>
              
              <div class="text-center">
                <p class="mb-0">Already have an account? 
                  <a routerLink="/login" class="text-decoration-none">Login here</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .card {
      border: none;
      border-radius: 15px;
    }
    
    .card-header {
      border-radius: 15px 15px 0 0 !important;
    }
    
    .form-control {
      border-radius: 10px;
    }
    
    .btn {
      border-radius: 10px;
    }
    
    .alert {
      border-radius: 10px;
    }
    
    .text-muted {
      font-size: 0.875rem;
    }
  `]
})
export class RegisterComponent {
  customer: Customer = {
    fullName: '',
    email: '',
    password: '',
    address: '',
    contactNumber: ''
  };
  
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private customerService: CustomerService,
    private router: Router
  ) {}

  onRegister(): void {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.customerService.registerCustomer(this.customer).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.successMessage = `Registration successful! Customer ID: ${response.customerId}. Redirecting to login...`;
        
        // Reset form
        this.customer = {
          fullName: '',
          email: '',
          password: '',
          address: '',
          contactNumber: ''
        };
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          this.router.navigate(['/login']);
        }, 3000);
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Registration failed. Please try again.';
      }
    });
  }
}
