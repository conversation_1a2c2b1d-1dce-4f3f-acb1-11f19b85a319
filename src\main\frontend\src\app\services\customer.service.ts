import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Customer, CustomerResponse, LoginRequest, LoginResponse } from '../models/customer.model';

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private apiUrl = '/api/customers';
  private authUrl = '/api/auth';

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  /**
   * Customer Registration - US002
   */
  registerCustomer(customer: Customer): Observable<CustomerResponse> {
    return this.http.post<CustomerResponse>(`${this.apiUrl}/register`, customer, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Customer Login
   */
  login(loginRequest: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.authUrl}/login`, loginRequest, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update Customer Details - US003
   */
  updateCustomer(customerId: number, customer: Customer): Observable<CustomerResponse> {
    return this.http.put<CustomerResponse>(`${this.apiUrl}/${customerId}`, customer, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Search Customer By Name - US005
   */
  searchCustomerByName(customerName: string): Observable<CustomerResponse[]> {
    return this.http.get<CustomerResponse[]>(`${this.apiUrl}/search?name=${encodeURIComponent(customerName)}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get customer by ID
   */
  getCustomerById(customerId: number): Observable<CustomerResponse> {
    return this.http.get<CustomerResponse>(`${this.apiUrl}/${customerId}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all customers
   */
  getAllCustomers(): Observable<CustomerResponse[]> {
    return this.http.get<CustomerResponse[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete customer
   */
  deleteCustomer(customerId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${customerId}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Logout
   */
  logout(): Observable<any> {
    return this.http.post(`${this.authUrl}/logout`, {}, this.httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 400) {
        errorMessage = error.error?.message || 'Bad request';
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    console.error('CustomerService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
