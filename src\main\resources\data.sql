-- Insert default admin user
INSERT INTO admins (username, password, full_name, email, role, is_active, created_at, updated_at) 
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'System Administrator', '<EMAIL>', 'SUPER_ADMIN', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample customers
INSERT INTO customers (full_name, email, password, address, contact_number, created_at, updated_at) 
VALUES 
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '123 Main Street, City, State 12345', '1234567890', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('<PERSON>', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '456 Oak Avenue, City, State 12345', '0987654321', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Bob Johnson', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '789 Pine Road, City, State 12345', '5555555555', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample products
INSERT INTO products (product_name, price, quantity, reserved, customer_id, created_at, updated_at) 
VALUES 
('Organic Apples', 4.99, 100, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Fresh Bananas', 2.49, 150, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Whole Milk', 3.79, 50, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Brown Bread', 2.99, 75, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Free Range Eggs', 5.49, 40, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Organic Carrots', 3.29, 80, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Greek Yogurt', 4.79, 60, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Chicken Breast', 8.99, 30, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Salmon Fillet', 12.99, 25, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Olive Oil', 7.49, 35, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Pasta', 1.99, 120, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Rice', 3.99, 90, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Tomatoes', 2.79, 70, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Onions', 1.89, 85, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Potatoes', 2.49, 95, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Broccoli', 3.49, 45, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Spinach', 2.99, 55, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Bell Peppers', 4.29, 40, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Cheese', 6.99, 35, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Orange Juice', 4.49, 50, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample orders
INSERT INTO orders (customer_id, order_date, order_amount, status, created_at, updated_at) 
VALUES 
(1, CURRENT_TIMESTAMP, 25.47, 'DELIVERED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, CURRENT_TIMESTAMP, 18.73, 'SHIPPED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, CURRENT_TIMESTAMP, 32.96, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price, created_at, updated_at) 
VALUES 
-- Order 1 items
(1, 1, 2, 4.99, 9.98, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 3, 1, 3.79, 3.79, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 4, 2, 2.99, 5.98, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 5, 1, 5.49, 5.49, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Order 2 items
(2, 2, 3, 2.49, 7.47, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 6, 2, 3.29, 6.58, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 7, 1, 4.79, 4.79, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Order 3 items
(3, 8, 1, 8.99, 8.99, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, 9, 1, 12.99, 12.99, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, 10, 1, 7.49, 7.49, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3, 11, 2, 1.99, 3.98, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
