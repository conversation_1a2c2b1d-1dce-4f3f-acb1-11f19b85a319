package com.grocery.repository;

import com.grocery.model.Customer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class CustomerRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String INSERT_CUSTOMER = 
        "INSERT INTO customers (full_name, email, password, address, contact_number, created_at, updated_at) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_CUSTOMER = 
        "UPDATE customers SET full_name = ?, email = ?, password = ?, address = ?, contact_number = ?, updated_at = ? " +
        "WHERE customer_id = ?";
    
    private static final String FIND_BY_ID = 
        "SELECT * FROM customers WHERE customer_id = ?";
    
    private static final String FIND_BY_EMAIL = 
        "SELECT * FROM customers WHERE email = ?";
    
    private static final String FIND_ALL = 
        "SELECT * FROM customers ORDER BY created_at DESC";
    
    private static final String FIND_BY_NAME_IGNORE_CASE = 
        "SELECT * FROM customers WHERE LOWER(full_name) LIKE LOWER(?) ORDER BY full_name";
    
    private static final String DELETE_BY_ID = 
        "DELETE FROM customers WHERE customer_id = ?";
    
    private static final String EXISTS_BY_EMAIL = 
        "SELECT COUNT(*) FROM customers WHERE email = ?";
    
    private static final String EXISTS_BY_EMAIL_AND_NOT_ID = 
        "SELECT COUNT(*) FROM customers WHERE email = ? AND customer_id != ?";
    
    private final RowMapper<Customer> customerRowMapper = new CustomerRowMapper();
    
    public Customer save(Customer customer) {
        LocalDateTime now = LocalDateTime.now();
        
        if (customer.getCustomerId() == null) {
            // Insert new customer
            KeyHolder keyHolder = new GeneratedKeyHolder();
            
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(INSERT_CUSTOMER, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, customer.getFullName());
                ps.setString(2, customer.getEmail());
                ps.setString(3, customer.getPassword());
                ps.setString(4, customer.getAddress());
                ps.setString(5, customer.getContactNumber());
                ps.setObject(6, now);
                ps.setObject(7, now);
                return ps;
            }, keyHolder);
            
            customer.setCustomerId(keyHolder.getKey().longValue());
            customer.setCreatedAt(now);
            customer.setUpdatedAt(now);
        } else {
            // Update existing customer
            customer.setUpdatedAt(now);
            jdbcTemplate.update(UPDATE_CUSTOMER,
                customer.getFullName(),
                customer.getEmail(),
                customer.getPassword(),
                customer.getAddress(),
                customer.getContactNumber(),
                customer.getUpdatedAt(),
                customer.getCustomerId()
            );
        }
        
        return customer;
    }
    
    public Optional<Customer> findById(Long id) {
        try {
            Customer customer = jdbcTemplate.queryForObject(FIND_BY_ID, customerRowMapper, id);
            return Optional.ofNullable(customer);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public Optional<Customer> findByEmail(String email) {
        try {
            Customer customer = jdbcTemplate.queryForObject(FIND_BY_EMAIL, customerRowMapper, email);
            return Optional.ofNullable(customer);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public List<Customer> findAll() {
        return jdbcTemplate.query(FIND_ALL, customerRowMapper);
    }
    
    public List<Customer> findByNameIgnoreCase(String name) {
        String searchPattern = "%" + name + "%";
        return jdbcTemplate.query(FIND_BY_NAME_IGNORE_CASE, customerRowMapper, searchPattern);
    }
    
    public void deleteById(Long id) {
        jdbcTemplate.update(DELETE_BY_ID, id);
    }
    
    public boolean existsByEmail(String email) {
        Integer count = jdbcTemplate.queryForObject(EXISTS_BY_EMAIL, Integer.class, email);
        return count != null && count > 0;
    }
    
    public boolean existsByEmailAndNotId(String email, Long customerId) {
        Integer count = jdbcTemplate.queryForObject(EXISTS_BY_EMAIL_AND_NOT_ID, Integer.class, email, customerId);
        return count != null && count > 0;
    }
    
    private static class CustomerRowMapper implements RowMapper<Customer> {
        @Override
        public Customer mapRow(ResultSet rs, int rowNum) throws SQLException {
            Customer customer = new Customer();
            customer.setCustomerId(rs.getLong("customer_id"));
            customer.setFullName(rs.getString("full_name"));
            customer.setEmail(rs.getString("email"));
            customer.setPassword(rs.getString("password"));
            customer.setAddress(rs.getString("address"));
            customer.setContactNumber(rs.getString("contact_number"));
            customer.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
            customer.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
            return customer;
        }
    }
}
