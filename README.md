# Online Grocery Ordering System

A comprehensive online grocery ordering system built with Java Spring Boot, Angular, and H2 database. This system provides a complete solution for managing customers, products, and orders with robust security features including SQL injection prevention.

## 📋 Project Overview

This project implements a full-featured online grocery ordering system with the following capabilities:

-   Customer registration and management
-   Product catalog management
-   Order processing and tracking
-   Admin panel with menu-driven interface
-   Search functionality for customers and products
-   SQL injection prevention and security measures
-   RESTful API endpoints
-   Responsive web interface

## 🏗️ Architecture

-   **Backend**: Spring Boot 3.2.0 with Java 17
-   **Frontend**: Angular 17 with TypeScript
-   **Database**: H2 in-memory database with JDBC Template
-   **Security**: Spring Security with role-based access control
-   **UI Framework**: Bootstrap 5 with responsive design
-   **Build Tool**: Maven with frontend integration

## 🚀 Prerequisites

-   Java 17 or higher
-   Node.js 18+ and npm
-   Maven 3.6+
-   Git

## ⚡ Quick Start

1. **Clone the repository**

    ```bash
    git clone <repository-url>
    cd Online-Grocery-Ordering-System-1
    ```

2. **Build and run the application**

    ```bash
    mvn clean install
    mvn spring-boot:run
    ```

3. **Access the application**
    - Main Application: http://localhost:8080
    - H2 Database Console: http://localhost:8080/h2-console
    - Angular Frontend: http://localhost:8080/app

## 🔐 Demo Credentials

### Admin Access

-   **Username**: admin
-   **Password**: admin123

### Customer Access

-   **Email**: <EMAIL>
-   **Password**: Password123!

## 📚 User Stories Implementation

### US001: Menu-driven System

The system provides a comprehensive menu with 9 options:

1. Customer Registration
2. Update Customer Details
3. Get Customer Order Details
4. Customer Search
5. Product Search
6. Register Product
7. Update Product
8. Delete Product
9. Exit

### US002: Customer Registration

-   Complete customer registration with validation
-   Password complexity requirements
-   Email and phone number validation
-   Address management

### US003: Customer Update

-   Update customer details
-   Role-based access control
-   Data validation and sanitization

### US004: Customer Order Details

-   View customer order history
-   Order tracking and status updates
-   Detailed order information

### US005: Customer Search by Name

-   Case-insensitive search functionality
-   SQL injection prevention
-   Admin-only access

### US006: Product Search by Name

-   Product catalog search
-   Available to both customers and admins
-   Real-time search results

### US007: Product Registration

-   Add new products to inventory
-   Product details management
-   Admin-only functionality

### US008: Product Update

-   Modify existing product information
-   Inventory management
-   Price and quantity updates

### US009: Product Deletion

-   Remove products from catalog
-   Admin-only access
-   Cascade deletion handling

### US0010: SQL Injection Prevention

-   Comprehensive input validation
-   Parameterized queries
-   Input sanitization utilities
-   Security best practices

## 🛡️ Security Features

-   **SQL Injection Prevention**: All database queries use parameterized statements
-   **Input Validation**: Comprehensive validation on all user inputs
-   **Authentication**: Spring Security with role-based access
-   **Password Encryption**: BCrypt password encoding
-   **CSRF Protection**: Cross-site request forgery protection
-   **Session Management**: Secure session handling

## 📁 Project Structure

```
src/
├── main/
│   ├── java/com/grocery/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # REST controllers and web controllers
│   │   ├── dto/            # Data Transfer Objects
│   │   ├── model/          # JPA entities
│   │   ├── repository/     # JDBC repositories
│   │   ├── security/       # Security utilities
│   │   └── service/        # Business logic services
│   ├── resources/
│   │   ├── application.properties
│   │   └── data.sql        # Sample data
│   ├── webapp/
│   │   ├── WEB-INF/jsp/    # JSP pages
│   │   └── static/         # CSS and JS files
│   └── frontend/           # Angular application
└── test/                   # Test classes
```

## 🔧 Configuration

### Database Configuration

The application uses H2 in-memory database with the following settings:

-   **URL**: jdbc:h2:mem:grocerydb
-   **Username**: sa
-   **Password**: (empty)
-   **Console**: Enabled at /h2-console

### Application Properties

Key configuration settings in `application.properties`:

-   Server port: 8080
-   JSP view resolver configuration
-   H2 database settings
-   Security configuration
-   Session management

## 🌐 API Endpoints

### Customer APIs

-   `POST /api/customers/register` - Register new customer
-   `PUT /api/customers/{id}` - Update customer details
-   `GET /api/customers/search` - Search customers by name
-   `GET /api/customers/{id}` - Get customer by ID

### Product APIs

-   `POST /api/products/register` - Register new product
-   `PUT /api/products/{id}` - Update product details
-   `DELETE /api/products/{id}` - Delete product
-   `GET /api/products/search` - Search products by name

### Order APIs

-   `POST /api/orders` - Create new order
-   `GET /api/orders/customer/{id}` - Get customer orders
-   `PUT /api/orders/{id}` - Update order
-   `PATCH /api/orders/{id}/status` - Update order status

## 🧪 Testing

Run the test suite:

```bash
mvn test
```

## 📦 Build and Deployment

### Development Build

```bash
mvn clean compile
mvn spring-boot:run
```

### Production Build

```bash
mvn clean package
java -jar target/online-grocery-ordering-system-1.0.0.jar
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author

**Chirag Singhal** (GitHub: `chirag127`)

## 📅 Last Updated

2025-08-01T14:45:41.486Z

---

_This project demonstrates a complete enterprise-level application with modern Java technologies, security best practices, and comprehensive functionality for online grocery ordering._
