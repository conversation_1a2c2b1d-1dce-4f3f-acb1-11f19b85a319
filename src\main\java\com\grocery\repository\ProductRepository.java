package com.grocery.repository;

import com.grocery.model.Product;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class ProductRepository {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String INSERT_PRODUCT = 
        "INSERT INTO products (product_name, price, quantity, reserved, customer_id, created_at, updated_at) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_PRODUCT = 
        "UPDATE products SET product_name = ?, price = ?, quantity = ?, reserved = ?, customer_id = ?, updated_at = ? " +
        "WHERE product_id = ?";
    
    private static final String FIND_BY_ID = 
        "SELECT * FROM products WHERE product_id = ?";
    
    private static final String FIND_ALL = 
        "SELECT * FROM products ORDER BY product_name";
    
    private static final String FIND_BY_NAME_IGNORE_CASE = 
        "SELECT * FROM products WHERE LOWER(product_name) LIKE LOWER(?) ORDER BY product_name";
    
    private static final String DELETE_BY_ID = 
        "DELETE FROM products WHERE product_id = ?";
    
    private static final String EXISTS_BY_ID = 
        "SELECT COUNT(*) FROM products WHERE product_id = ?";
    
    private static final String UPDATE_QUANTITY = 
        "UPDATE products SET quantity = ?, updated_at = ? WHERE product_id = ?";
    
    private static final String UPDATE_RESERVED = 
        "UPDATE products SET reserved = ?, updated_at = ? WHERE product_id = ?";
    
    private final RowMapper<Product> productRowMapper = new ProductRowMapper();
    
    public Product save(Product product) {
        LocalDateTime now = LocalDateTime.now();
        
        if (product.getProductId() == null) {
            // Insert new product
            KeyHolder keyHolder = new GeneratedKeyHolder();
            
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(INSERT_PRODUCT, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, product.getProductName());
                ps.setBigDecimal(2, product.getPrice());
                ps.setInt(3, product.getQuantity());
                ps.setInt(4, product.getReserved() != null ? product.getReserved() : 0);
                if (product.getCustomerId() != null) {
                    ps.setLong(5, product.getCustomerId());
                } else {
                    ps.setNull(5, java.sql.Types.BIGINT);
                }
                ps.setObject(6, now);
                ps.setObject(7, now);
                return ps;
            }, keyHolder);
            
            product.setProductId(keyHolder.getKey().longValue());
            product.setCreatedAt(now);
            product.setUpdatedAt(now);
        } else {
            // Update existing product
            product.setUpdatedAt(now);
            jdbcTemplate.update(UPDATE_PRODUCT,
                product.getProductName(),
                product.getPrice(),
                product.getQuantity(),
                product.getReserved() != null ? product.getReserved() : 0,
                product.getCustomerId(),
                product.getUpdatedAt(),
                product.getProductId()
            );
        }
        
        return product;
    }
    
    public Optional<Product> findById(Long id) {
        try {
            Product product = jdbcTemplate.queryForObject(FIND_BY_ID, productRowMapper, id);
            return Optional.ofNullable(product);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    public List<Product> findAll() {
        return jdbcTemplate.query(FIND_ALL, productRowMapper);
    }
    
    public List<Product> findByNameIgnoreCase(String name) {
        String searchPattern = "%" + name + "%";
        return jdbcTemplate.query(FIND_BY_NAME_IGNORE_CASE, productRowMapper, searchPattern);
    }
    
    public void deleteById(Long id) {
        jdbcTemplate.update(DELETE_BY_ID, id);
    }
    
    public boolean existsById(Long id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS_BY_ID, Integer.class, id);
        return count != null && count > 0;
    }
    
    public void updateQuantity(Long productId, Integer quantity) {
        jdbcTemplate.update(UPDATE_QUANTITY, quantity, LocalDateTime.now(), productId);
    }
    
    public void updateReserved(Long productId, Integer reserved) {
        jdbcTemplate.update(UPDATE_RESERVED, reserved, LocalDateTime.now(), productId);
    }
    
    private static class ProductRowMapper implements RowMapper<Product> {
        @Override
        public Product mapRow(ResultSet rs, int rowNum) throws SQLException {
            Product product = new Product();
            product.setProductId(rs.getLong("product_id"));
            product.setProductName(rs.getString("product_name"));
            product.setPrice(rs.getBigDecimal("price"));
            product.setQuantity(rs.getInt("quantity"));
            product.setReserved(rs.getInt("reserved"));
            
            Long customerId = rs.getLong("customer_id");
            if (!rs.wasNull()) {
                product.setCustomerId(customerId);
            }
            
            product.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
            product.setUpdatedAt(rs.getTimestamp("updated_at").toLocalDateTime());
            return product;
        }
    }
}
