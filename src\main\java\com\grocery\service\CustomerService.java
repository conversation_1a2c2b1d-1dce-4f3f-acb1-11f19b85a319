package com.grocery.service;

import com.grocery.dto.CustomerDTO;
import com.grocery.model.Customer;
import com.grocery.repository.CustomerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class CustomerService {
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * Customer Registration - US002
     */
    public CustomerDTO customerRegistration(CustomerDTO customerDTO) {
        // Validate unique email
        if (customerRepository.existsByEmail(customerDTO.getEmail())) {
            throw new RuntimeException("Email address is already registered. Please use a different email.");
        }
        
        // Create customer entity
        Customer customer = new Customer();
        customer.setFullName(customerDTO.getFullName());
        customer.setEmail(customerDTO.getEmail());
        customer.setPassword(passwordEncoder.encode(customerDTO.getPassword()));
        customer.setAddress(customerDTO.getAddress());
        customer.setContactNumber(customerDTO.getContactNumber());
        
        // Save customer
        Customer savedCustomer = customerRepository.save(customer);
        
        // Convert to DTO and return
        return convertToDTO(savedCustomer);
    }
    
    /**
     * Update Customer Details - US003
     */
    public CustomerDTO customerUpdate(Long customerId, CustomerDTO customerDTO) {
        Optional<Customer> existingCustomerOpt = customerRepository.findById(customerId);
        if (existingCustomerOpt.isEmpty()) {
            throw new RuntimeException("Customer not found with ID: " + customerId);
        }
        
        Customer existingCustomer = existingCustomerOpt.get();
        
        // Validate unique email if email is being updated
        if (!existingCustomer.getEmail().equals(customerDTO.getEmail())) {
            if (customerRepository.existsByEmailAndNotId(customerDTO.getEmail(), customerId)) {
                throw new RuntimeException("Email address is already registered by another customer.");
            }
        }
        
        // Update customer details
        existingCustomer.setFullName(customerDTO.getFullName());
        existingCustomer.setEmail(customerDTO.getEmail());
        
        // Update password only if provided
        if (customerDTO.getPassword() != null && !customerDTO.getPassword().trim().isEmpty()) {
            existingCustomer.setPassword(passwordEncoder.encode(customerDTO.getPassword()));
        }
        
        existingCustomer.setAddress(customerDTO.getAddress());
        existingCustomer.setContactNumber(customerDTO.getContactNumber());
        
        // Save updated customer
        Customer updatedCustomer = customerRepository.save(existingCustomer);
        
        return convertToDTO(updatedCustomer);
    }
    
    /**
     * Search Customer By Name - US005
     */
    public List<CustomerDTO> searchCustomerByName(String customerName) {
        if (customerName == null || customerName.trim().isEmpty()) {
            throw new RuntimeException("Customer name cannot be empty");
        }
        
        List<Customer> customers = customerRepository.findByNameIgnoreCase(customerName.trim());
        
        if (customers.isEmpty()) {
            throw new RuntimeException("Customer not found");
        }
        
        return customers.stream()
                .map(this::convertToSecureDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Get customer by ID
     */
    public CustomerDTO getCustomerById(Long customerId) {
        Optional<Customer> customerOpt = customerRepository.findById(customerId);
        if (customerOpt.isEmpty()) {
            throw new RuntimeException("Customer not found with ID: " + customerId);
        }
        
        return convertToDTO(customerOpt.get());
    }
    
    /**
     * Get customer by email
     */
    public CustomerDTO getCustomerByEmail(String email) {
        Optional<Customer> customerOpt = customerRepository.findByEmail(email);
        if (customerOpt.isEmpty()) {
            throw new RuntimeException("Customer not found with email: " + email);
        }
        
        return convertToDTO(customerOpt.get());
    }
    
    /**
     * Get all customers
     */
    public List<CustomerDTO> getAllCustomers() {
        List<Customer> customers = customerRepository.findAll();
        return customers.stream()
                .map(this::convertToSecureDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * Authenticate customer
     */
    public boolean authenticateCustomer(String email, String password) {
        Optional<Customer> customerOpt = customerRepository.findByEmail(email);
        if (customerOpt.isEmpty()) {
            return false;
        }
        
        Customer customer = customerOpt.get();
        return passwordEncoder.matches(password, customer.getPassword());
    }
    
    /**
     * Delete customer
     */
    public void deleteCustomer(Long customerId) {
        if (!customerRepository.findById(customerId).isPresent()) {
            throw new RuntimeException("Customer not found with ID: " + customerId);
        }
        
        customerRepository.deleteById(customerId);
    }
    
    /**
     * Convert Customer entity to DTO
     */
    private CustomerDTO convertToDTO(Customer customer) {
        CustomerDTO dto = new CustomerDTO();
        dto.setCustomerId(customer.getCustomerId());
        dto.setFullName(customer.getFullName());
        dto.setEmail(customer.getEmail());
        dto.setAddress(customer.getAddress());
        dto.setContactNumber(customer.getContactNumber());
        dto.setCreatedAt(customer.getCreatedAt());
        dto.setUpdatedAt(customer.getUpdatedAt());
        // Note: Password is not included in DTO for security
        return dto;
    }
    
    /**
     * Convert Customer entity to secure DTO (with encrypted password display)
     */
    private CustomerDTO convertToSecureDTO(Customer customer) {
        CustomerDTO dto = convertToDTO(customer);
        // Show encrypted password format for admin view
        dto.setPassword("****ENCRYPTED****");
        return dto;
    }
}
