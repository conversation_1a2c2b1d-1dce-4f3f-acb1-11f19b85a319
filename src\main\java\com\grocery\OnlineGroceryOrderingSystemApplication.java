package com.grocery;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Online Grocery Ordering System - Main Application Class
 * 
 * This is a complete online grocery ordering system built with:
 * - Spring Boot 3.2.0
 * - Java 17
 * - H2 Database
 * - JDBC Template for database operations
 * - Spring Security for authentication
 * - JSP for server-side rendering
 * - Angular for frontend
 * - SQL injection prevention
 * 
 * Features implemented:
 * - US001: Menu-driven system with 9 options
 * - US002: Customer registration
 * - US003: Customer update
 * - US004: Customer order details
 * - US005: Customer search by name
 * - US006: Product search by name
 * - US007: Product registration
 * - US008: Product update
 * - US009: Product deletion
 * - US0010: SQL injection prevention
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
@SpringBootApplication
@EnableTransactionManagement
@EntityScan(basePackages = "com.grocery.model")
@EnableJpaRepositories(basePackages = "com.grocery.repository")
@ComponentScan(basePackages = "com.grocery")
public class OnlineGroceryOrderingSystemApplication {

    public static void main(String[] args) {
        System.out.println("=================================================");
        System.out.println("  Online Grocery Ordering System Starting...   ");
        System.out.println("=================================================");
        System.out.println("  Author: Chirag Singhal                       ");
        System.out.println("  Version: 1.0.0                               ");
        System.out.println("  Java Version: " + System.getProperty("java.version"));
        System.out.println("  Spring Boot Version: 3.2.0                   ");
        System.out.println("=================================================");
        
        SpringApplication.run(OnlineGroceryOrderingSystemApplication.class, args);
        
        System.out.println("=================================================");
        System.out.println("  Application Started Successfully!            ");
        System.out.println("  Access URLs:                                  ");
        System.out.println("  - Main Application: http://localhost:8080    ");
        System.out.println("  - H2 Console: http://localhost:8080/h2-console");
        System.out.println("  - Admin Login: admin / admin123              ");
        System.out.println("  - Customer Login: <EMAIL> / Password123!");
        System.out.println("=================================================");
    }
}
