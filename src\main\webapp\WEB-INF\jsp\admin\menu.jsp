<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Menu - Online Grocery Ordering System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/admin/menu">
                <i class="fas fa-shopping-cart me-2"></i>
                Online Grocery Store - Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, <sec:authentication property="name"/>
                </span>
                <a class="btn btn-outline-light btn-sm" href="/logout">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-5">
        <!-- Success/Error Messages -->
        <c:if test="${not empty success}">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                ${success}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>
        
        <c:if test="${not empty error}">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                ${error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>
        
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-cogs me-2"></i>Admin Menu - US001</h3>
                        <p class="mb-0">Choose an option from the menu below</p>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- Option 1: Customer Registration -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-plus fa-3x text-success mb-3"></i>
                                        <h5 class="card-title">1. Customer Registration</h5>
                                        <p class="card-text">Register new customers in the system</p>
                                        <a href="/admin/customer/register" class="btn btn-success">
                                            <i class="fas fa-user-plus me-2"></i>Register Customer
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 2: Update Customer Details -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-edit fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">2. Update Customer Details</h5>
                                        <p class="card-text">Modify existing customer information</p>
                                        <a href="/admin/customer/update" class="btn btn-warning">
                                            <i class="fas fa-user-edit me-2"></i>Update Customer
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 3: Get Customer Order Details -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-shopping-bag fa-3x text-info mb-3"></i>
                                        <h5 class="card-title">3. Customer Order Details</h5>
                                        <p class="card-text">View customer order history and details</p>
                                        <a href="/admin/customer/orders" class="btn btn-info">
                                            <i class="fas fa-shopping-bag me-2"></i>View Orders
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 4: Customer Search -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">4. Customer Search</h5>
                                        <p class="card-text">Search customers by name</p>
                                        <a href="/admin/customer/search" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>Search Customer
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 5: Product Search -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-box-open fa-3x text-secondary mb-3"></i>
                                        <h5 class="card-title">5. Product Search</h5>
                                        <p class="card-text">Search products by name</p>
                                        <a href="/admin/product/search" class="btn btn-secondary">
                                            <i class="fas fa-box-open me-2"></i>Search Product
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 6: Register Product -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-plus-circle fa-3x text-success mb-3"></i>
                                        <h5 class="card-title">6. Register Product</h5>
                                        <p class="card-text">Add new products to inventory</p>
                                        <a href="/admin/product/register" class="btn btn-success">
                                            <i class="fas fa-plus-circle me-2"></i>Register Product
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 7: Update Product -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-edit fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">7. Update Product</h5>
                                        <p class="card-text">Modify existing product details</p>
                                        <a href="/admin/product/update" class="btn btn-warning">
                                            <i class="fas fa-edit me-2"></i>Update Product
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 8: Delete Product -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-danger">
                                    <div class="card-body text-center">
                                        <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                                        <h5 class="card-title">8. Delete Product</h5>
                                        <p class="card-text">Remove products from inventory</p>
                                        <a href="/admin/product/delete" class="btn btn-danger">
                                            <i class="fas fa-trash-alt me-2"></i>Delete Product
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Option 9: Exit -->
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100 border-dark">
                                    <div class="card-body text-center">
                                        <i class="fas fa-sign-out-alt fa-3x text-dark mb-3"></i>
                                        <h5 class="card-title">9. Exit</h5>
                                        <p class="card-text">Logout from admin panel</p>
                                        <a href="/logout" class="btn btn-dark">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
