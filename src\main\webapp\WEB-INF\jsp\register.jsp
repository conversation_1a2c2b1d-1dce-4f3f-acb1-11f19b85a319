<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Online Grocery Ordering System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white text-center">
                        <h3><i class="fas fa-user-plus me-2"></i>Customer Registration</h3>
                        <p class="mb-0">Join Our Online Grocery Store</p>
                    </div>
                    <div class="card-body">
                        <!-- Error Message -->
                        <c:if test="${not empty error}">
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                ${error}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </c:if>
                        
                        <form:form action="/customer/register" method="post" modelAttribute="customerDTO">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="fullName" class="form-label">Full Name *</label>
                                        <form:input path="fullName" class="form-control" id="fullName" 
                                                   placeholder="Enter your full name" required="true"/>
                                        <form:errors path="fullName" cssClass="text-danger"/>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <form:input path="email" type="email" class="form-control" id="email" 
                                                   placeholder="Enter your email" required="true"/>
                                        <form:errors path="email" cssClass="text-danger"/>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password *</label>
                                        <form:password path="password" class="form-control" id="password" 
                                                      placeholder="Enter your password" required="true"/>
                                        <form:errors path="password" cssClass="text-danger"/>
                                        <small class="text-muted">
                                            Password must be at least 8 characters with uppercase, lowercase, digit, and special character
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contactNumber" class="form-label">Contact Number *</label>
                                        <form:input path="contactNumber" class="form-control" id="contactNumber" 
                                                   placeholder="Enter 10-digit contact number" required="true"/>
                                        <form:errors path="contactNumber" cssClass="text-danger"/>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Address *</label>
                                <form:textarea path="address" class="form-control" id="address" rows="3"
                                              placeholder="Enter your complete address" required="true"/>
                                <form:errors path="address" cssClass="text-danger"/>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-user-plus me-2"></i>Register
                                </button>
                            </div>
                        </form:form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="mb-0">Already have an account? 
                                <a href="/login" class="text-decoration-none">Login here</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
